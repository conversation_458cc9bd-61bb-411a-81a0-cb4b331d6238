function enhanced_speech = kalman_filter(signal, noise, fs)
    N = length(signal);  
    len_win = 1;               % 帧长1s
    shift_percent = 0.5;       % 窗移占比
    AR_order = 7;              % 滤波器阶数
    iter = 20;                 %迭代次数设置

    % 分帧加窗处理
    len_winframe = fix(len_win * fs);
    window = ones(len_winframe,1);
    [y, num_frame] = KFrame(signal, len_winframe, window, shift_percent);

    % 初始化
    H = [zeros(1, AR_order-1), 1];   % 观测矩阵
    R = var(noise);                       % 噪声方差
    [filt_coeff, Q] = lpc(y, AR_order);  % LPC预测，得到滤波器的系数
    C = R * eye(AR_order, AR_order);     % 误差协方差矩阵
    enhanced_speech = zeros(1, length(signal)); % 增强后的语音信号
    enhanced_speech(1:AR_order) = signal(1:AR_order); %初始化
    updata_x = signal(1:AR_order);

    % 迭代器的次数
    i = AR_order + 1;
    j = AR_order + 1;

    % 卡尔曼滤波
    for k = 1:num_frame   %一次处理一帧信号
        jStart = j;     %跟踪每次迭代AR_Order+1的值.
        OutputOld = updata_x;    %为每次迭代保留第一批AROrder预估量

        for l = 1:iter               %迭代次数
            fai = [zeros(AR_order-1, 1) eye(AR_order-1); fliplr(-filt_coeff(k, 2:end))];

            for ii = i:len_winframe
                % 卡尔曼滤波
                predict_x = fai * updata_x;
                predict_C = (fai * C * fai') + (H' * Q(k) * H);
                K = (predict_C * H') / ((H * predict_C * H') + R);
                updata_x = predict_x + (K * (y(ii, k) - (H * predict_x)));
                enhanced_speech(j-AR_order+1:j) = updata_x';
                C = (eye(AR_order) - K * H) * predict_C;
                j = j + 1;
            end
            i = 1;
            if l < iter
                j = jStart;
                updata_x = OutputOld;
            end
            % 更新滤波后信号的lpc
            [filt_coeff(k, :), Q(k)] = lpc(enhanced_speech((k-1)*len_winframe+1:k*len_winframe), AR_order);
        end
    end
    enhanced_speech = enhanced_speech(1:N)';
end







