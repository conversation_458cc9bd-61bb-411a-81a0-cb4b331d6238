% clear all
% clc
% close all
% 
% %% 选择多个.mat文件
% M_Folders = "Body_Sound";  % 目标文件夹
% [file, path] = uigetfile(fullfile(M_Folders, '*.mat'), 'Select MAT files', 'MultiSelect', 'on'); 
% 
% % 如果用户没有选择文件，显示提示并退出
% if isequal(file, 0)
%     disp('用户取消了文件选择');
%     return;
% end
% 
% % 如果只选择了一个文件，转换为 cell 数组（方便后续统一处理）
% if ischar(file)
%     file = {file};  % 将文件名转换为 cell 数组
% end
% 
% %% 创建一个新的文件夹存放处理后的数据文件
% outputFolder = fullfile(path, 'Processed_Files');  % 处理后的文件夹路径
% 
% % 如果文件夹不存在，则创建它
% if ~exist(outputFolder, 'dir')
%     mkdir(outputFolder);
%     disp(['新文件夹创建: ', outputFolder]);
% end
% 
% %% 遍历每个选中的.mat文件
% for i = 1:length(file)
%     % 获取当前文件的完整路径
%     filename = fullfile(path, file{i});
% 
%     % 加载.mat文件
%     load(filename);  % 假设文件中包含 'allTime', 'allColumn2', 'allColumn3', 'samplingRate'
% 
%     % 提取数据
%     mic1 = column2;
%     mic2 = column3;
%     t1 = time;           % 时间刻度
% 
%     % 将列数据转换为时间表格式
%     tt1 = timetable(seconds(t1), mic1);
%     tt2 = timetable(seconds(t1), mic2);
% 
%     % 保存为新文件，文件名加上 "_tt" 后缀，且只保存 tt1 和 tt2
%     [~, name, ~] = fileparts(filename);  % 获取文件名（不包括路径和扩展名）
%     newFileName = fullfile(outputFolder, [name, '_tt.mat']);  % 新文件名，保存到新文件夹
% 
%     % 保存时间表数据到新文件，只保存 tt1 和 tt2
%     save(newFileName, 'tt1', 'tt2');
% 
%     % 输出处理进度
%     disp(['文件处理完成: ', newFileName]);
% end










clear all
clc
close all

%% 选择多个.mat文件
M_Folders = "Body_Sound";  % 目标文件夹
[file, path] = uigetfile(fullfile(M_Folders, '*.mat'), 'Select MAT files', 'MultiSelect', 'on'); 

% 如果用户没有选择文件，显示提示并退出
if isequal(file, 0)
    disp('用户取消了文件选择');
    return;
end

% 如果只选择了一个文件，转换为 cell 数组（方便后续统一处理）
if ischar(file)
    file = {file};  % 将文件名转换为 cell 数组
end

%% 创建一个新的文件夹存放处理后的数据文件
outputFolder = fullfile(path, 'Processed_Files');  % 处理后的文件夹路径

% 如果文件夹不存在，则创建它
if ~exist(outputFolder, 'dir')
    mkdir(outputFolder);
    disp(['新文件夹创建: ', outputFolder]);
end

%% 遍历每个选中的.mat文件
for i = 1:length(file)
    % 获取当前文件的完整路径
    filename = fullfile(path, file{i});
    
    % 加载.mat文件
    load(filename);  % 假设文件中包含 'allTime', 'allColumn2', 'allColumn3', 'samplingRate'

    % 提取数据
    mic1 = column2;
    mic2 = column3;
    t1 = time;           % 时间刻度

    % 将列数据转换为时间表格式
    % tt1 = timetable(t1, mic1);
    % tt2 = timetable(t1, mic2);
    
    fs = 2570;  % 采样率
    % 方法 1：使用采样率创建时间表
    tt1 = timetable(mic1, 'SampleRate', fs);
    tt2 = timetable(mic2, 'SampleRate', fs);

    
    % 保存为新文件，文件名加上 "_tt" 后缀，且只保存 tt1 和 tt2
    [~, name, ~] = fileparts(filename);  % 获取文件名（不包括路径和扩展名）
    newFileName = fullfile(outputFolder, [name, '_tt.mat']);  % 新文件名，保存到新文件夹

    % 保存时间表数据到新文件，只保存 tt1 和 tt2
    % save(newFileName, 'tt1', 'tt2');
    save(newFileName, 'tt1');
    
    % 输出处理进度
    disp(['文件处理完成: ', newFileName]);
end
