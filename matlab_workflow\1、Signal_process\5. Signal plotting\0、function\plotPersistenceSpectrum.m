function plotPersistenceSpectrum(y, fs, timeLimits, frequencyLimits, overlapPercent)
    % plotPersistenceSpectrum - 计算并绘制音频信号的持久谱图
    %
    % 语法: plotPersistenceSpectrum(y, fs, timeLimits, frequencyLimits, overlapPercent)
    %
    % 输入参数:
    %   y - 音频信号数据
    %   fs - 采样频率
    %   timeLimits - 感兴趣的信号时间区域（秒）
    %   frequencyLimits - 频率范围（Hz）
    %   overlapPercent - 重叠百分比

    % 将音频信号转换为时间表格式
    t = (0:length(y)-1)' / fs;
    tt = timetable(seconds(t), y);

    % 对感兴趣的信号时间区域进行索引
    tt_y_ROI = tt(:,'y');
    tt_y_ROI = tt_y_ROI(timerange(timeLimits(1), timeLimits(2), 'closed'), 1);

    % 计算并绘制持久谱图
    figure('Position', [300, 300, 900, 600]); % 设置图框位置和大小，[left, bottom, width, height]
    % 创建一个新的图形窗口
  pspectrum(tt_y_ROI, ...
        'persistence', ...
        'FrequencyLimits', frequencyLimits, ...
        'OverlapPercent', overlapPercent, ...
        'Leakage', 0.85); % 增加泄漏参数以平滑谱图

   
    ax = gca;
    ax.FontSize = 14; % 设置刻度尺文字大小
    ax.FontName = 'Times New Roman'; % 设置刻度尺字体
    ax.FontWeight = "bold";
    ax.XLim = [0 900];
    ax.YLim = [-120 -35];
    xlabel('Frequency (Hz)', 'FontSize', 16, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
    ylabel('Power spectrum (dB)', 'FontSize', 16, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
    title('Persistence spectrum', 'FontSize', 16, 'FontName', 'Times New Roman', 'FontWeight', 'bold');


    % 刷新图形窗口
    drawnow;
end