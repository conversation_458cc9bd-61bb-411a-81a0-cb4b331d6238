function bowel_sound_gui()
    % 创建主界面窗口
    fig = figure('Name', 'Bowel Sound GUI', 'NumberTitle', 'off', ...
                 'Position', [100, 100, 800, 600]);
    
    % 创建用于绘制波形的坐标轴
    ax = axes('Parent', fig, 'Position', [0.1, 0.3, 0.85, 0.65]);
    xlabel(ax, 'Time (s)');
    ylabel(ax, 'Amplitude');
    title(ax, 'Bowel Sound Waveform');
    
    % 初始化变量
    current_csv_file = '';
    current_data = [];
    current_time = [];
    
    % 创建按钮区域的面板
    btn_panel = uipanel('Parent', fig, 'Position', [0.1, 0.05, 0.85, 0.2]);
    
    % 读取数据按钮
    btn_read = uicontrol('Parent', btn_panel, 'Style', 'pushbutton', ...
                         'String', '读取数据', 'FontSize', 12, ...
                         'Units', 'normalized', 'Position', [0.05, 0.6, 0.2, 0.3], ...
                         'Callback', @read_data_callback);
    
    % 分类按钮
    btn_SB = uicontrol('Parent', btn_panel, 'Style', 'pushbutton', ...
                       'String', 'SB', 'FontSize', 12, ...
                       'Units', 'normalized', 'Position', [0.3, 0.6, 0.15, 0.3], ...
                       'Callback', @(src, event) classify_callback('SB'));
                   
    btn_MB = uicontrol('Parent', btn_panel, 'Style', 'pushbutton', ...
                       'String', 'MB', 'FontSize', 12, ...
                       'Units', 'normalized', 'Position', [0.47, 0.6, 0.15, 0.3], ...
                       'Callback', @(src, event) classify_callback('MB'));
                   
    btn_CRS = uicontrol('Parent', btn_panel, 'Style', 'pushbutton', ...
                        'String', 'CRS', 'FontSize', 12, ...
                        'Units', 'normalized', 'Position', [0.64, 0.6, 0.15, 0.3], ...
                        'Callback', @(src, event) classify_callback('CRS'));
                    
    btn_HS = uicontrol('Parent', btn_panel, 'Style', 'pushbutton', ...
                       'String', 'HS', 'FontSize', 12, ...
                       'Units', 'normalized', 'Position', [0.81, 0.6, 0.15, 0.3], ...
                       'Callback', @(src, event) classify_callback('HS'));
    
    % 完成按钮
    btn_finish = uicontrol('Parent', btn_panel, 'Style', 'pushbutton', ...
                           'String', '完成', 'FontSize', 12, ...
                           'Units', 'normalized', 'Position', [0.4, 0.1, 0.2, 0.3], ...
                           'Callback', @finish_callback);
    
    % 读取数据的回调函数
    function read_data_callback(src, event)
        [file, path] = uigetfile('*.csv', '选择一个CSV文件');
        if isequal(file, 0)
            disp('用户取消了文件选择');
            return;
        end
        current_csv_file = fullfile(path, file);
        data_table = readtable(current_csv_file);
        current_time = data_table{:, 1};  % 假设第一列是时间
        current_data = data_table{:, 2};  % 假设第二列是信号
        cla(ax);  % 清除当前坐标轴内容
        % 调用新的 plot_waveform 函数，在指定的坐标轴上绘图
        plot_waveform(ax, current_time, current_data, 'Bowel Sound Waveform');
    end

    % 分类按钮的回调函数
    function classify_callback(label)
        if isempty(current_csv_file)
            errordlg('请先读取一个CSV文件！', '错误');
            return;
        end
        [filepath, name, ext] = fileparts(current_csv_file);
        new_name = [name, '_', label, ext];
        new_file = fullfile(filepath, new_name);
        % 重命名文件
        movefile(current_csv_file, new_file);
        msgbox(['文件已重命名为：', new_name], '提示');
        % 更新当前文件路径
        current_csv_file = new_file;
    end

    % 完成按钮的回调函数
    function finish_callback(src, event)
        cla(ax);  % 清除坐标轴内容
        current_csv_file = '';
        current_data = [];
        current_time = [];
        msgbox('已完成当前波形的处理，可以读取下一个波形。', '提示');
    end
end



function plot_waveform(ax, t, data, title_text)
    % 在指定的坐标轴上绘制波形
    plot(ax, t, data);
    ax.FontSize = 16; % 设置刻度尺文字大小
    ax.FontName = 'Times New Roman'; % 设置刻度尺字体
    ax.FontWeight = 'bold';
    ax.YLim = [-1 1];
    ax.XLim = [min(t) max(t)]; % 自动调整横坐标范围为时间序列的最小值和最大值
    yticks(ax, [-1 -0.5 0 0.5 1]); % 自定义纵坐标刻度
    xlabel(ax, 'Time(s)', 'FontSize', 18, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
    ylabel(ax, 'Normalized amplitude', 'FontSize', 18, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
    title(ax, title_text, 'FontSize', 18, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
end
