% 
% % 1. test_kalman_filter 函数，遍历不同参数组合并调用 kalman_filter 和 calculate_snr
% function test_kalman_filter(signal, noise, fs)
%     % 定义参数范围
%     len_win_values = [0.25, 0.5, 1, 1.5, 2];        % 帧长（秒）
%     shift_percent_values = [0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7]; % 窗移占比
%     AR_order_values = 1:10;                         % 滤波器阶数
%     iter_values = 1:10;                             % 迭代次数
% 
%     % 开启并行池（如果还没有开启）
%     if isempty(gcp('nocreate'))
%         parpool('local'); % 自动启动本地并行池
%     end
% 
%     % 用 parfor 来进行并行运算
%     parfor idx = 1:length(len_win_values) * length(shift_percent_values) * length(AR_order_values) * length(iter_values)
%         % 获取参数组合的索引
%         [len_win_idx, shift_percent_idx, AR_order_idx, iter_idx] = ind2sub([length(len_win_values), length(shift_percent_values), length(AR_order_values), length(iter_values)], idx);
% 
%         % 获取当前参数
%         len_win = len_win_values(len_win_idx);
%         shift_percent = shift_percent_values(shift_percent_idx);
%         AR_order = AR_order_values(AR_order_idx);
%         iter = iter_values(iter_idx);
% 
%         % 应用卡尔曼滤波
%         enhanced_speech = kalman_filter(signal, noise, fs, len_win, shift_percent, AR_order, iter);
% 
%         % 计算滤波后的SNR
%         snr_value = calculate_snr(signal, enhanced_speech);
% 
%         % 打印当前参数和对应的SNR值
%         fprintf('len_win=%.2f, shift_percent=%.2f, AR_order=%d, iter=%d, SNR=%.4f dB\n', ...
%             len_win, shift_percent, AR_order, iter, snr_value);
%     end
% end

% 主程序，用于测试不同参数组合并绘制 SNR 图形
function test_kalman_filter_plot(signal, noise, fs)
    % 定义参数范围
    len_win_values = [0.25, 0.5, 1, 1.5, 2];        % 帧长（秒）
    shift_percent_values = [0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7]; % 窗移占比
    AR_order_values = 6:10;                         % 滤波器阶数
    iter_values = 6:10;                             % 迭代次数

    % 准备存储 SNR 数据的线性数组
    total_combinations = length(len_win_values) * length(shift_percent_values) * length(AR_order_values) * length(iter_values);
    snr_results_linear = zeros(total_combinations, 1);  % 用线性数组存储结果

    % 开启并行池（如果还没有开启）
    if isempty(gcp('nocreate'))
        parpool('local'); % 自动启动本地并行池
    end

    % 设置并行池数据队列，用于传递进度
    progress_queue = parallel.pool.DataQueue;
    
    % 设置进度显示器
    afterEach(progress_queue, @update_progress);
    
    % 初始化进度
    progress = 0;
    total_iterations = total_combinations;
    
    % 进度显示回调函数
    function update_progress(~)
        progress = progress + 1;
        fprintf('Progress: %.2f%%\n', (progress / total_iterations) * 100);
    end
    
    % 并行计算，收集 SNR 数据
    parfor idx = 1:total_combinations
        % 获取参数组合的索引
        [len_win_idx, shift_percent_idx, AR_order_idx, iter_idx] = ind2sub([length(len_win_values), length(shift_percent_values), length(AR_order_values), length(iter_values)], idx);

        % 获取当前参数
        len_win = len_win_values(len_win_idx);
        shift_percent = shift_percent_values(shift_percent_idx);
        AR_order = AR_order_values(AR_order_idx);
        iter = iter_values(iter_idx);

        % 应用卡尔曼滤波
        enhanced_speech = kalman_filter(signal, noise, fs, len_win, shift_percent, AR_order, iter);

        % 计算滤波后的SNR
        snr_value = calculate_snr(signal, enhanced_speech);
        
        % 存储 SNR 值到线性数组
        snr_results_linear(idx) = snr_value;

        % 打印当前参数和对应的SNR值
        fprintf('len_win=%.2f, shift_percent=%.2f, AR_order=%d, iter=%d, SNR=%.4f dB\n', ...
                len_win, shift_percent, AR_order, iter, snr_value);
        
        % 更新进度
        send(progress_queue, idx);
    end

    % 关闭并行池
    delete(gcp('nocreate'));

    % 将线性 SNR 结果转换为多维数组
    snr_results = reshape(snr_results_linear, [length(len_win_values), length(shift_percent_values), length(AR_order_values), length(iter_values)]);

    % 绘制图形来显示 SNR 变化
    plot_snr_results(len_win_values, shift_percent_values, AR_order_values, iter_values, snr_results);
end

% 用于绘制 SNR 变化的函数
function plot_snr_results(len_win_values, shift_percent_values, AR_order_values, iter_values, snr_results)
    % 选择要绘制的两个维度，例如 AR_order 和 iter
    % 你可以选择不同的参数维度来查看它们对 SNR 的影响
    [X, Y] = meshgrid(AR_order_values, iter_values);
    
    % 绘制每种帧长和窗移的 SNR 变化
    for len_win_idx = 1:length(len_win_values)
        for shift_percent_idx = 1:length(shift_percent_values)
            figure;
            Z = squeeze(snr_results(len_win_idx, shift_percent_idx, :, :)); % 提取 SNR 结果
            surf(X, Y, Z); % 创建3D图像
            title(sprintf('SNR for len_win=%.2f sec, shift_percent=%.2f', len_win_values(len_win_idx), shift_percent_values(shift_percent_idx)));
            xlabel('AR Order');
            ylabel('Iterations');
            zlabel('SNR (dB)');
            colorbar;
            grid on;
        end
    end
end

% 其他辅助函数，如 kalman_filter 和 calculate_snr 保持不变





% 2. kalman_filter 函数，进行卡尔曼滤波
function enhanced_speech = kalman_filter(signal, noise, fs, len_win, shift_percent, AR_order, iter)
    N = length(signal);  
    len_winframe = fix(len_win * fs);       % 根据帧长设置帧长对应的采样点数
    window = ones(len_winframe,1);          % 使用矩形窗
    [y, num_frame] = KFrame(signal, len_winframe, window, shift_percent); % 分帧

    % 初始化
    H = [zeros(1, AR_order-1), 1];         % 观测矩阵
    R = var(noise);                        % 噪声方差
    [filt_coeff, Q] = lpc(y, AR_order);    % LPC预测，得到滤波器的系数
    C = R * eye(AR_order, AR_order);       % 误差协方差矩阵
    enhanced_speech = zeros(1, length(signal)); % 增强后的语音信号
    enhanced_speech(1:AR_order) = signal(1:AR_order); % 初始化
    updata_x = signal(1:AR_order);

    % 迭代器的次数
    i = AR_order + 1;
    j = AR_order + 1;

    % 卡尔曼滤波
    for k = 1:num_frame
        jStart = j;        % 跟踪每次迭代AR_Order+1的值.
        OutputOld = updata_x;    % 为每次迭代保留第一批AROrder预估量

        for l = 1:iter
            fai = [zeros(AR_order-1, 1) eye(AR_order-1); fliplr(-filt_coeff(k, 2:end))];

            for ii = i:len_winframe
                % 卡尔曼滤波
                predict_x = fai * updata_x;
                predict_C = (fai * C * fai') + (H' * Q(k) * H);
                K = (predict_C * H') / ((H * predict_C * H') + R);
                updata_x = predict_x + (K * (y(ii, k) - (H * predict_x)));
                enhanced_speech(j-AR_order+1:j) = updata_x';
                C = (eye(AR_order) - K * H) * predict_C;
                j = j + 1;
            end
            i = 1;
            if l < iter
                j = jStart;
                updata_x = OutputOld;
            end
            % 更新滤波后信号的lpc
            [filt_coeff(k, :), Q(k)] = lpc(enhanced_speech((k-1)*len_winframe+1:k*len_winframe), AR_order);
        end
    end
    enhanced_speech = enhanced_speech(1:N)'; % 输出增强后的语音信号
end

% 3. calculate_snr 函数，用于计算滤波后信号的SNR
function snr_filtered = calculate_snr(In, filtered_signal)
    % 计算信号的能量
    Ps = sum(filtered_signal.^2);
    
    % 计算噪声的能量（带噪信号 - 滤波后的信号）
    Pn = sum((In - filtered_signal).^2);
    
    % 计算滤波后的 SNR
    snr_filtered = 10 * log10(Ps / Pn);   % 单位：dB
    
    % 返回计算出的SNR值
    return
end
